// Defensive DOM manipulation with error handling
let shadowHost, shadowRoot, popup, styleSheet, buttonContainer;

function initializeShadowDOM() {
  try {
    // Check if Shadow DOM is supported
    if (!document.body || !document.body.attachShadow) {
      console.warn('Shadow DOM not supported, falling back to regular DOM');
      return initializeFallbackDOM();
    }

    // Create Shadow DOM container for complete style isolation
    shadowHost = document.createElement('div');
    shadowHost.id = 'gemini-translator-shadow-host';
    shadowHost.style.cssText = `
      position: absolute !important;
      z-index: 2147483647 !important;
      pointer-events: none !important;
      top: 0 !important;
      left: 0 !important;
      width: 0 !important;
      height: 0 !important;
    `;

    // Create shadow root with closed mode for maximum isolation
    shadowRoot = shadowHost.attachShadow({ mode: 'closed' });

    // Create popup container inside shadow DOM
    popup = document.createElement('div');
    popup.className = 'gemini-translator-popup';
    popup.style.display = 'none';

    // Safely append to body with error handling
    if (document.body) {
      document.body.appendChild(shadowHost);
    } else {
      // Wait for body to be available
      document.addEventListener('DOMContentLoaded', () => {
        if (document.body) {
          document.body.appendChild(shadowHost);
        }
      });
    }

    shadowRoot.appendChild(popup);

    // Inject isolated styles into shadow DOM
    styleSheet = document.createElement('style');
    styleSheet.textContent = getIsolatedStyles();
    shadowRoot.appendChild(styleSheet);

    // Create button container in shadow DOM
    buttonContainer = document.createElement('div');
    buttonContainer.id = 'gemini-translator-button-container';
    shadowRoot.appendChild(buttonContainer);

    return true;
  } catch (error) {
    console.error('Failed to initialize Shadow DOM:', error);
    return initializeFallbackDOM();
  }
}

// Fallback DOM initialization for unsupported browsers
function initializeFallbackDOM() {
  try {
    // Create regular DOM elements with high specificity classes
    popup = document.createElement('div');
    popup.className = generateClassName('popup');
    popup.style.display = 'none';
    popup.style.position = 'absolute';
    popup.style.zIndex = '2147483647';

    // Create button container
    buttonContainer = document.createElement('div');
    buttonContainer.id = 'gemini-translator-button-container';
    buttonContainer.style.position = 'absolute';
    buttonContainer.style.zIndex = '2147483647';

    // Inject styles into document head
    styleSheet = document.createElement('style');
    styleSheet.textContent = getFallbackStyles();

    if (document.head) {
      document.head.appendChild(styleSheet);
    }

    if (document.body) {
      document.body.appendChild(popup);
      document.body.appendChild(buttonContainer);
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        if (document.body) {
          document.body.appendChild(popup);
          document.body.appendChild(buttonContainer);
        }
      });
    }

    // Set fallback mode
    shadowRoot = null;
    shadowHost = null;

    return true;
  } catch (error) {
    console.error('Failed to initialize fallback DOM:', error);
    return false;
  }
}

// CSS class name generator with high specificity and uniqueness
const CSS_PREFIX = 'gt-ext-v2'; // Gemini Translator Extension v2
const generateClassName = (base) => `${CSS_PREFIX}__${base}`;

// Function to get completely isolated styles with high specificity
function getIsolatedStyles() {
  const btnClass = generateClassName('button');
  const popupClass = generateClassName('popup');
  const contentClass = generateClassName('content');
  const cardClass = generateClassName('card');
  const translationClass = generateClassName('translation');
  const errorClass = generateClassName('error');
  const headerClass = generateClassName('header');
  const closeClass = generateClassName('close');
  const messageClass = generateClassName('message');

  return `
    /* CSS Reset for Shadow DOM with high specificity */
    :host *, :host *::before, :host *::after {
      box-sizing: border-box !important;
      margin: 0 !important;
      padding: 0 !important;
      border: 0 !important;
      font-size: 100% !important;
      font: inherit !important;
      vertical-align: baseline !important;
      background: transparent !important;
      text-decoration: none !important;
      list-style: none !important;
      outline: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    /* Import Google Fonts with fallbacks */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Roboto:wght@400;500&display=swap');

    /* Root container styles */
    :host {
      all: initial;
      position: absolute;
      z-index: 2147483647;
      pointer-events: none;
      font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Button styles with high specificity */
    :host .${btnClass} {
      position: absolute !important;
      width: 16px !important;
      height: 16px !important;
      padding: 2px !important;
      border: none !important;
      border-radius: 4px !important;
      background: rgba(255, 255, 255, 0.95) !important;
      backdrop-filter: blur(8px) saturate(150%) !important;
      -webkit-backdrop-filter: blur(8px) saturate(150%) !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
      pointer-events: auto !important;
      z-index: 1 !important;
      font-family: inherit !important;
    }

    :host .${btnClass} svg {
      width: 10px !important;
      height: 10px !important;
      stroke: #2563eb !important;
      stroke-width: 2.5 !important;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
    }

    :host .${btnClass}:hover {
      background: rgba(255, 255, 255, 1) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.3) !important;
      transform: translateY(-1px) !important;
    }

    :host .${btnClass}:active {
      transform: translateY(0) !important;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    }

    :host .${btnClass}.${generateClassName('loading')} {
      animation: pulse 1.5s ease-in-out infinite !important;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.8; transform: scale(1.05); }
    }

    /* Popup styles with high specificity */
    :host .${popupClass} {
      position: absolute !important;
      max-width: 600px !important;
      min-width: 280px !important;
      width: auto !important;
      background: transparent !important;
      border: none !important;
      border-radius: 0.5rem !important;
      padding: 0 !important;
      overflow: hidden !important;
      pointer-events: auto !important;
      z-index: 1 !important;
      font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    }

    :host .${contentClass} {
      position: relative !important;
      display: flex !important;
      flex-direction: column !important;
      gap: 8px !important;
      color: #1a1a1a !important;
    }

    :host .${cardClass} {
      position: relative !important;
      display: flex !important;
      flex-direction: column !important;
      border-radius: 0.5rem !important;
      background-color: #18385a !important;
      padding: 6px !important;
      overflow: hidden !important;
      box-shadow:
        inset 0 0 30px rgba(255, 255, 255, 0.08),
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05) !important;
      backdrop-filter: blur(20px) saturate(150%) !important;
      -webkit-backdrop-filter: blur(20px) saturate(150%) !important;
    }

    :host .${cardClass}::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      height: 70% !important;
      background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.12),
        rgba(255, 255, 255, 0.05),
        transparent
      ) !important;
      pointer-events: none !important;
      border-radius: 0.5rem 0.5rem 2rem 2rem !important;
      backdrop-filter: blur(1px) !important;
      opacity: 0.8 !important;
    }

    :host .${translationClass} {
      color: #94a3b8 !important;
      line-height: 1.6 !important;
      font-size: 0.875rem !important;
      word-wrap: break-word !important;
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      text-rendering: optimizeLegibility !important;
      position: relative !important;
      overflow: hidden !important;
      padding: 8px !important;
      margin: 2px !important;
      background: rgba(33, 40, 48, 0.1) !important;
      backdrop-filter: blur(8px) !important;
      border-radius: 6px !important;
      font-family: inherit !important;
    }

    :host .${translationClass} p {
      margin: 6px 0 !important;
      color: #94a3b8 !important;
      font-size: 0.875rem !important;
      font-family: inherit !important;
    }

    :host .${translationClass} h1,
    :host .${translationClass} h2,
    :host .${translationClass} h3 {
      margin: 8px 0 !important;
      font-weight: 600 !important;
      color: #94a3b8 !important;
      font-family: inherit !important;
    }

    :host .${translationClass} pre {
      white-space: pre-wrap !important;
      word-wrap: break-word !important;
      background-color: rgba(255, 255, 255, 0.05) !important;
      padding: 8px !important;
      border-radius: 4px !important;
      color: #94a3b8 !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
    }

    :host .${translationClass} code {
      background-color: rgba(255, 255, 255, 0.05) !important;
      padding: 2px 4px !important;
      border-radius: 3px !important;
      color: #94a3b8 !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
    }

    /* Error styles with high specificity */
    :host .${contentClass}.${errorClass} {
      background: rgba(239, 68, 68, 0.1) !important;
      border: 1px solid rgba(239, 68, 68, 0.2) !important;
    }

    :host .${headerClass} {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      padding: 12px 16px !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
      font-weight: 600 !important;
      color: #f3f4f6 !important;
      font-family: inherit !important;
    }

    :host .${closeClass} {
      width: 24px !important;
      height: 24px !important;
      border-radius: 4px !important;
      border: none !important;
      background: transparent !important;
      color: rgba(255, 255, 255, 0.7) !important;
      font-size: 18px !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.2s !important;
      font-family: inherit !important;
    }

    :host .${closeClass}:hover {
      background: rgba(255, 255, 255, 0.1) !important;
      color: rgba(255, 255, 255, 0.9) !important;
    }

    :host .${messageClass} {
      padding: 12px 16px !important;
      color: #fca5a5 !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      font-weight: 500 !important;
      font-family: inherit !important;
    }

    /* Animations with high specificity */
    @keyframes ${CSS_PREFIX}-fadeInUp {
      from {
        opacity: 0 !important;
        transform: translateY(10px) scale(0.95) !important;
        filter: blur(5px) !important;
      }
      to {
        opacity: 1 !important;
        transform: translateY(0) scale(1) !important;
        filter: blur(0) !important;
      }
    }

    @keyframes ${CSS_PREFIX}-pulse {
      0%, 100% {
        opacity: 1 !important;
        transform: scale(1) !important;
      }
      50% {
        opacity: 0.8 !important;
        transform: scale(1.05) !important;
      }
    }

    :host .${popupClass} {
      animation: ${CSS_PREFIX}-fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
    }
  `;
}

// Fallback styles for browsers without Shadow DOM support
function getFallbackStyles() {
  const btnClass = generateClassName('button');
  const popupClass = generateClassName('popup');
  const contentClass = generateClassName('content');
  const cardClass = generateClassName('card');
  const translationClass = generateClassName('translation');
  const errorClass = generateClassName('error');
  const headerClass = generateClassName('header');
  const closeClass = generateClassName('close');
  const messageClass = generateClassName('message');

  return `
    /* Fallback styles with maximum specificity */
    html body .${btnClass} {
      position: absolute !important;
      width: 16px !important;
      height: 16px !important;
      padding: 2px !important;
      border: none !important;
      border-radius: 4px !important;
      background: rgba(255, 255, 255, 0.95) !important;
      backdrop-filter: blur(8px) saturate(150%) !important;
      -webkit-backdrop-filter: blur(8px) saturate(150%) !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
      pointer-events: auto !important;
      z-index: 2147483647 !important;
      font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    }

    html body .${popupClass} {
      position: absolute !important;
      max-width: 600px !important;
      min-width: 280px !important;
      width: auto !important;
      background: transparent !important;
      border: none !important;
      border-radius: 0.5rem !important;
      padding: 0 !important;
      overflow: hidden !important;
      pointer-events: auto !important;
      z-index: 2147483647 !important;
      font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    }

    html body .${cardClass} {
      position: relative !important;
      display: flex !important;
      flex-direction: column !important;
      border-radius: 0.5rem !important;
      background-color: #18385a !important;
      padding: 6px !important;
      overflow: hidden !important;
      box-shadow:
        inset 0 0 30px rgba(255, 255, 255, 0.08),
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05) !important;
      backdrop-filter: blur(20px) saturate(150%) !important;
      -webkit-backdrop-filter: blur(20px) saturate(150%) !important;
    }

    html body .${translationClass} {
      color: #94a3b8 !important;
      line-height: 1.6 !important;
      font-size: 0.875rem !important;
      word-wrap: break-word !important;
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
      text-rendering: optimizeLegibility !important;
      position: relative !important;
      overflow: hidden !important;
      padding: 8px !important;
      margin: 2px !important;
      background: rgba(33, 40, 48, 0.1) !important;
      backdrop-filter: blur(8px) !important;
      border-radius: 6px !important;
      font-family: inherit !important;
    }
  `;
}

// Generate custom font styles for user preferences
function generateCustomFontStyles(fontFamily, fontSize) {
  const defaultFont = 'Inter, Roboto, -apple-system, BlinkMacSystemFont, system-ui, sans-serif';
  const defaultSize = '15';

  if (fontFamily === defaultFont && fontSize === defaultSize) {
    return null; // Use default styles
  }

  const size = parseInt(fontSize, 10) || 15;
  const lineHeight = size > 20 ? 1.8 : 1.6;

  const translationClass = generateClassName('translation');

  return `
    :host .${translationClass} {
      font-family: ${fontFamily} !important;
      font-size: ${size}px !important;
      line-height: ${lineHeight} !important;
    }
    :host .${translationClass} p {
      font-family: ${fontFamily} !important;
      font-size: ${size}px !important;
    }
    :host .${translationClass} h1,
    :host .${translationClass} h2,
    :host .${translationClass} h3 {
      font-family: ${fontFamily} !important;
    }
  `;
}

function initializeContentScript() {
  try {
    // Initialize DOM with error handling
    const initialized = initializeShadowDOM();
    if (!initialized) {
      console.error('Failed to initialize extension DOM');
      return;
    }

    // Add event listeners with error handling
    document.addEventListener('mouseup', handleSelectionEvent);
    document.addEventListener('keyup', handleSelectionEvent);

    chrome.runtime.onMessage.addListener((message) => {
      try {
        handleBackgroundMessages(message);
      } catch (error) {
        console.error('Error handling background message:', error);
      }
    });

  } catch (error) {
    console.error('Failed to initialize content script:', error);
  }
}

function handleSelectionEvent(event) {
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();

  if (!selectedText) {
    hidePopup();
    return;
  }

  let position;
  try {
    if (event && event.type === 'mouseup' && event.clientX != null && event.clientY != null) {
      position = {
        x: event.clientX + window.scrollX + 10,
        y: event.clientY + window.scrollY
      };
    } else if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      position = {
        x: rect.left + window.scrollX + (rect.width / 2),
        y: rect.bottom + window.scrollY
      };
    } else {
      hidePopup();
      return;
    }
  } catch (error) {
    console.error("Error getting selection position:", error);
    hidePopup();
    return;
  }


  showTranslationButton(position, selectedText);
}

function handleBackgroundMessages(message) {

  switch (message.action) {
    case 'showTranslation':
      showTranslationPopup(message.translation, message.position);
      break;
    case 'showError':
      showErrorPopup(message.error, message.position);
      break;
    case 'translateText':
      const selection = window.getSelection();
      if (selection.toString().trim() === message.text.trim()) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        const position = {
          x: rect.left + window.scrollX + (rect.width / 2),
          y: rect.bottom + window.scrollY
        };
        sendMessageToBackground({
          action: 'translate',
          text: message.text,

          position
        });
      }
      break;
    case 'updateFontStyle':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    case 'updateFontSize':
      if (popup.style.display === 'block') {
        updatePopupStyles();
      }
      break;
    default:

      break;
  }
}

function sendMessageToBackground(message) {
  chrome.runtime.sendMessage(message);
}

function showTranslationButton(position, text) {
  try {
    const btnClass = generateClassName('button');
    let button;

    if (shadowRoot) {
      // Shadow DOM mode
      const container = shadowRoot.getElementById('gemini-translator-button-container');
      if (!container) {
        console.error('Button container not found in shadow DOM');
        return;
      }
      button = container.querySelector(`.${btnClass}`);

      if (!button) {
        button = document.createElement('button');
        button.className = btnClass;
        container.appendChild(button);

        button.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M5 8l6 6"></path>
            <path d="M4 14l6-6 2-3"></path>
            <path d="M2 5h12"></path>
            <path d="M7 2h1"></path>
            <path d="M22 22l-5-10-5 10"></path>
            <path d="M14 18h6"></path>
          </svg>
        `;
      }

      // Position the shadow host container
      if (shadowHost) {
        shadowHost.style.cssText = `
          position: absolute !important;
          z-index: 2147483647 !important;
          pointer-events: none !important;
          left: ${position.x}px !important;
          top: ${position.y}px !important;
          width: 16px !important;
          height: 16px !important;
        `;
      }
    } else {
      // Fallback mode
      if (!buttonContainer) {
        console.error('Button container not found in fallback mode');
        return;
      }
      button = buttonContainer.querySelector(`.${btnClass}`);

      if (!button) {
        button = document.createElement('button');
        button.className = btnClass;
        buttonContainer.appendChild(button);

        button.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M5 8l6 6"></path>
            <path d="M4 14l6-6 2-3"></path>
            <path d="M2 5h12"></path>
            <path d="M7 2h1"></path>
            <path d="M22 22l-5-10-5 10"></path>
            <path d="M14 18h6"></path>
          </svg>
        `;
      }

      // Position button directly
      button.style.left = `${position.x}px`;
      button.style.top = `${position.y}px`;
    }

    button.style.display = 'flex';

    button.onclick = function () {
      try {
        sendMessageToBackground({
          action: 'translate',
          text: text,
          position: position
        });
        button.classList.add(generateClassName('loading'));
      } catch (error) {
        console.error('Error sending translation request:', error);
      }
    };

  } catch (error) {
    console.error('Error showing translation button:', error);
  }
}

function formatAndSanitizeMarkdown(text) {
  if (!text || typeof text !== 'string') {
    return '<p>Translation not available.</p>';
  }

  try {
    marked.setOptions({
      sanitize: true,
      silent: true,
      headerIds: false,
      mangle: false
    });

    const rawHtml = marked.parse(text);
    const sanitizedHtml = DOMPurify.sanitize(rawHtml, {
      ALLOWED_TAGS: [
        // Basic formatting
        'p', 'br', 'strong', 'em', 'i', 'b', 'code', 'pre',
        // Lists
        'ul', 'ol', 'li',
        // Headings (limited to h3 and below for UI consistency)
        'h3', 'h4', 'h5', 'h6',
        // Other elements
        'blockquote', 'span'
      ],
      ALLOWED_ATTR: [],
      ADD_ATTR: ['class'],
      FORBID_TAGS: ['style', 'script', 'iframe', 'frame', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button'],
      FORBID_ATTR: ['style', 'onerror', 'onload', 'onclick', 'onmouseover'],
      ALLOW_DATA_ATTR: false,
      USE_PROFILES: { html: true },
      SANITIZE_DOM: true,
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_DOM_IMPORT: false,
      WHOLE_DOCUMENT: false,
      FORCE_BODY: true
    });

    return sanitizedHtml || '<p>Translation not available.</p>';
  } catch (error) {
    console.error('Error formatting markdown:', error);
    return '<p>Error formatting translation.</p>';
  }
}



function showTranslationPopup(translation, position) {
  try {
    // Hide button with error handling
    const btnClass = generateClassName('button');
    let button;

    if (shadowRoot) {
      const container = shadowRoot.getElementById('gemini-translator-button-container');
      if (container) {
        button = container.querySelector(`.${btnClass}`);
      }
    } else if (buttonContainer) {
      button = buttonContainer.querySelector(`.${btnClass}`);
    }

    if (button) {
      button.classList.remove(generateClassName('loading'));
      button.style.display = 'none';
    }

    const formattedTranslation = formatAndSanitizeMarkdown(translation);
    chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
      const fontFamily = fontResult.fontFamily || 'Inter, Roboto, -apple-system, BlinkMacSystemFont, system-ui, sans-serif';
      const fontSize = fontResult.fontSize || '15';

      // Apply custom font styles if different from default
      const customStyles = generateCustomFontStyles(fontFamily, fontSize);

      popup.className = generateClassName('popup');
      popup.innerHTML = `
      <div class="${generateClassName('content')}">
        <div class="${generateClassName('card')}">
          <div class="${generateClassName('translation')}">
            ${customStyles ? `<style>${customStyles}</style>` : ''}
            ${formattedTranslation}
          </div>
        </div>
      </div>
    `;

      positionPopup(position);
      popup.style.display = 'block';

      document.addEventListener('mousedown', closeOnClickOutside);
    });
  }

function showErrorPopup(error, position) {
    popup.className = generateClassName('popup');
    popup.innerHTML = `
    <div class="${generateClassName('content')} ${generateClassName('error')}">
      <div class="${generateClassName('header')}">
        <span>Translation Error</span>
        <button class="${generateClassName('close')}">&times;</button>
      </div>
      <div class="${generateClassName('message')}">${error}</div>
    </div>
  `;

    const closeBtn = popup.querySelector(`.${generateClassName('close')}`);
    if (closeBtn) {
      closeBtn.addEventListener('click', hidePopup);
    }

    positionPopup(position);
    popup.style.display = 'block';

    document.addEventListener('mousedown', closeOnClickOutside);
  }

  function updatePopupStyles() {
    chrome.storage.local.get(['fontFamily', 'fontSize'], function (fontResult) {
      const fontFamily = fontResult.fontFamily || 'Inter, Roboto, -apple-system, BlinkMacSystemFont, system-ui, sans-serif';
      const fontSize = fontResult.fontSize || '15';

      const customStyles = generateCustomFontStyles(fontFamily, fontSize);

      if (customStyles) {
        const styleElement = popup.querySelector('style');
        if (styleElement) {
          styleElement.textContent = customStyles;
        } else {
          const newStyle = document.createElement('style');
          newStyle.textContent = customStyles;
          popup.querySelector(`.${generateClassName('translation')}`).prepend(newStyle);
        }
      }
    });
  }

  function hidePopup() {
    popup.style.display = 'none';
    document.removeEventListener('mousedown', closeOnClickOutside);

    // Hide button in shadow DOM
    const buttonContainer = shadowRoot.getElementById('gemini-translator-button-container');
    const btnClass = generateClassName('button');
    const button = buttonContainer.querySelector(`.${btnClass}`);
    if (button) {
      button.style.display = 'none';
    }

    // Reset shadow host position
    shadowHost.style.cssText = `
    position: absolute !important;
    z-index: 2147483647 !important;
    pointer-events: none !important;
    top: 0 !important;
    left: 0 !important;
    width: 0 !important;
    height: 0 !important;
  `;
  }

  // Enhanced positioning system with better viewport detection
  function getViewportInfo() {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      scrollX: window.scrollX || window.pageXOffset || 0,
      scrollY: window.scrollY || window.pageYOffset || 0,
      // Safe margins to prevent popup from touching edges
      margin: 10
    };
  }

  function calculateOptimalPosition(targetPosition, popupDimensions, viewport, preferences) {
    const { width: popupWidth, height: popupHeight } = popupDimensions;
    const { width: viewWidth, height: viewHeight, scrollX, scrollY, margin } = viewport;
    const { autoPosition, defaultPosition } = preferences;

    // Available space in each direction
    const spaceAbove = targetPosition.y - scrollY - margin;
    const spaceBelow = scrollY + viewHeight - targetPosition.y - margin;

    let finalX = targetPosition.x - (popupWidth / 2); // Center horizontally by default
    let finalY = targetPosition.y;

    // Determine vertical position
    if (autoPosition) {
      // Smart positioning based on available space
      if (spaceBelow >= popupHeight) {
        // Enough space below
        finalY = targetPosition.y + margin;
      } else if (spaceAbove >= popupHeight) {
        // Not enough space below, try above
        finalY = targetPosition.y - popupHeight - margin;
      } else {
        // Not enough space above or below, position where there's more space
        if (spaceBelow > spaceAbove) {
          finalY = scrollY + viewHeight - popupHeight - margin;
        } else {
          finalY = scrollY + margin;
        }
      }
    } else {
      // Use user preference
      switch (defaultPosition) {
        case 'above':
          finalY = targetPosition.y - popupHeight - margin;
          break;
        case 'cursor':
          finalY = targetPosition.y;
          break;
        case 'below':
        default:
          finalY = targetPosition.y + margin;
          break;
      }
    }

    // Ensure horizontal bounds
    if (finalX < scrollX + margin) {
      finalX = scrollX + margin;
    } else if (finalX + popupWidth > scrollX + viewWidth - margin) {
      finalX = scrollX + viewWidth - popupWidth - margin;
    }

    // Ensure vertical bounds (final safety check)
    if (finalY < scrollY + margin) {
      finalY = scrollY + margin;
    } else if (finalY + popupHeight > scrollY + viewHeight - margin) {
      finalY = scrollY + viewHeight - popupHeight - margin;
    }

    return { x: finalX, y: finalY };
  }

  function positionPopup(position) {
    chrome.storage.local.get(['autoPosition', 'defaultPosition'], function (prefs) {
      const autoPositionEnabled = typeof prefs.autoPosition === 'boolean' ? prefs.autoPosition : true;
      const defaultPositionValue = typeof prefs.defaultPosition === 'string' ? prefs.defaultPosition : 'below';

      // Set initial position to make popup visible so we can measure it
      popup.style.left = '0px';
      popup.style.top = '0px';

      // Position shadow host for popup measurement
      shadowHost.style.cssText = `
      position: absolute !important;
      z-index: 2147483647 !important;
      pointer-events: none !important;
      left: 0px !important;
      top: 0px !important;
      width: auto !important;
      height: auto !important;
    `;

      // Use a setTimeout to ensure the popup has been rendered and has dimensions
      setTimeout(() => {
        try {
          // Get actual dimensions after rendering with fallbacks
          const popupRect = popup.getBoundingClientRect();
          const popupWidth = popupRect.width || popup.offsetWidth || 300;
          const popupHeight = popupRect.height || popup.offsetHeight || 150;

          // Get current viewport information
          const viewport = getViewportInfo();

          // Calculate optimal position using enhanced algorithm
          const optimalPosition = calculateOptimalPosition(
            position,
            { width: popupWidth, height: popupHeight },
            viewport,
            { autoPosition: autoPositionEnabled, defaultPosition: defaultPositionValue }
          );

          // Apply the calculated position to shadow host
          shadowHost.style.cssText = `
          position: absolute !important;
          z-index: 2147483647 !important;
          pointer-events: none !important;
          left: ${optimalPosition.x}px !important;
          top: ${optimalPosition.y}px !important;
          width: ${popupWidth}px !important;
          height: ${popupHeight}px !important;
        `;

          // Reset popup position within shadow DOM
          popup.style.left = '0px';
          popup.style.top = '0px';

        } catch (error) {
          console.error('Error positioning popup:', error);

          // Fallback positioning
          shadowHost.style.cssText = `
          position: absolute !important;
          z-index: 2147483647 !important;
          pointer-events: none !important;
          left: ${Math.max(10, position.x - 150)}px !important;
          top: ${position.y + 10}px !important;
          width: 300px !important;
          height: 150px !important;
        `;

          popup.style.left = '0px';
          popup.style.top = '0px';
        }
      }, 15); // Slightly longer timeout to ensure rendering
    });
  }

  function closeOnClickOutside(event) {
    // Check if click is outside shadow DOM
    if (!shadowHost.contains(event.target) && event.target !== shadowHost) {
      hidePopup();
    }
  }

  initializeContentScript();
